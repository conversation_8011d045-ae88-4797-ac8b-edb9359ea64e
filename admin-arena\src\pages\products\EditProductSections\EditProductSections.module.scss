@use '../../../scss/variables.scss' as *;
@use '../../../scss/mixins.scss' as *;

.container {
  // max-width: 1400px;
  // margin: 0 auto;
  padding: $spacing-6;
}

.header {
  @include flex-between;
  margin-bottom: $spacing-xl;
  padding-bottom: $spacing-lg;
  border-bottom: 1px solid $border-color;
}

.headerLeft {
  @include flex-start;
  gap: $spacing-lg;
}

.backButton {
  @include flex-center;
  gap: $spacing-sm;
  color: $text-secondary;

  &:hover {
    color: $primary-color;
  }
}

.titleSection {
  .title {
    @include heading-lg;
    margin: 0 0 $spacing-xs 0;
    color: $text-primary;
  }

  .subtitle {
    @include text-base;
    margin: 0;
    color: $text-secondary;
    font-weight: 500;
  }
}

.content {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: $spacing-6;
  align-items: start;
}

.sidebar {
  position: sticky !important;
  top: $spacing-lg;
}

.sidebarCard {
  .cardHeader {
    h3 {
      @include heading-sm;
      margin: 0;
      color: $text-primary;
    }
  }
}

.sectionNav {
  display: flex;
  flex-direction: column;
  gap: $spacing-xs;
}

.sectionButton {
  @include button-reset;
  display: flex;
  align-items: flex-start;
  gap: $spacing-md;
  padding: $spacing-md;
  border-radius: $border-radius;
  text-align: left;
  transition: all 0.2s ease;
  border: 1px solid transparent;

  &:hover {
    background-color: $background-hover;
    border-color: $border-color;
  }

  &.active {
    background-color: $primary-light;
    border-color: $primary-color;

    .sectionIcon {
      color: $primary-color;
    }

    .sectionTitle {
      color: $primary-color;
      font-weight: 600;
    }
  }
}

.sectionIcon {
  @include flex-center;
  width: 20px;
  height: 20px;
  color: $text-secondary;
  flex-shrink: 0;
  margin-top: 2px;

  svg {
    width: 18px;
    height: 18px;
  }
}

.sectionInfo {
  flex: 1;
  min-width: 0;
}

.sectionTitle {
  @include text-sm;
  font-weight: 500;
  color: $text-primary;
  margin-bottom: $spacing-xs;
}

.sectionDescription {
  @include text-xs;
  color: $text-secondary;
  line-height: 1.4;
}

.mainContent {
  min-height: 600px;
}

.sectionsContainer {
  display: flex;
  flex-direction: column;
  gap: $spacing-6;
}

.section {
  scroll-margin-top: $spacing-6;
}

.error {
  @include flex-center;
  flex-direction: column;
  gap: $spacing-lg;
  padding: $spacing-xl;
  text-align: center;

  h2 {
    @include heading-lg;
    color: $error-color;
    margin: 0;
  }

  p {
    @include text-base;
    color: $text-secondary;
    margin: 0;
  }
}

// Responsive design
@media (max-width: 1024px) {
  .content {
    grid-template-columns: 1fr;
    gap: $spacing-4;
  }

  .sidebar {
    position: static;
  }

  .sectionNav {
    flex-direction: row;
    overflow-x: auto;
    padding-bottom: $spacing-sm;

    .sectionButton {
      flex-shrink: 0;
      min-width: 200px;
    }
  }
}

@media (max-width: 768px) {
  .container {
    padding: $spacing-md;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-md;
  }

  .headerLeft {
    flex-direction: column;
    align-items: flex-start;
    gap: $spacing-sm;
  }

  .sectionNav {
    .sectionButton {
      min-width: 180px;

      .sectionDescription {
        display: none;
      }
    }
  }
}